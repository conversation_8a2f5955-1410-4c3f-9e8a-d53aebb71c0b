// const uploadsHooks = require('./hooks/uploadsHooks');
// const { authenticate } = require('@feathersjs/authentication');
import {GeneralError} from '@feathersjs/errors';

import {_get, _set} from "../../../utils/dash-utils.js";
import {uploadFile} from '../utils/uploader.js';
import {getStorjUrl} from '../storj/index.js';
import { getVideoUrl } from '../bunny/index.js';

// const uploadHookConfig = {
//   urlKeyName: 'file',
//   userKeyName: 'user',
//   publicRead: true,
//   singUrlKeyName: 'file'
// };

import {HookContext} from '../../../declarations.js';
import {AnyObj} from '../../../utils/types.js';
import {loadExists, setExists} from 'feathers-ucan';

export const handleUpload = async (context: HookContext): Promise<HookContext> => {

    if(!context.params.skip_upload) {
        let urlKeyName = 'url';


        try {
            const file = context.data.file || context.params.file
            const isFile = !!file && typeof file === 'object';
            const isUrlLink = !isFile && context.data[urlKeyName] && typeof context.data[urlKeyName] === 'string';

            if (isFile) {

                const originalname = _get(file, 'originalname', '');
                if(originalname) context.data.originalname = originalname;
                // if (context.params.uploadService) context.params.query?.storage = context.params.uploadService;
                if (typeof context.data?.info === 'string') context.data.info = JSON.parse(context.data.info);
                if(file.mimetype) context.data.info = { ...context.data.info, type: file.mimetype };
                context = await uploadFile(context)

            } else if (isUrlLink) {
               return context;
            }

        } catch (error) {
            console.error('file uploader upload', error);
            throw error;
        }
    }
    return context;
};

type GetOptions = {
    expires?:number
}
export const getLocalFile = (file: any, storage?: string, options?:GetOptions): (c: HookContext) => AnyObj | Promise<AnyObj> => {
    return async (context: HookContext) => {
        const serviceObj = {
            'storj': async () => getStorjUrl(file, { expires: options?.expires })(context),
            'bunny': () => getVideoUrl(file)
        }
        const fn = serviceObj[storage || context.result.storage]
        return fn ? Promise.resolve(fn())
            .catch(err => {
                console.error(err);
                throw new Error('failed to get signed url' + err.message);
            }) : context.result.url;
    };
};

export const handleFind = async (context: HookContext): Promise<HookContext> => {
    if (!context.params.joiningFile && !context.params.core?.skipJoins) {
        context.params = _set(context.params, 'joiningFile', {join: true});
        const joinedFiles = await Promise.all(context.result.data.map(async (a: AnyObj) => {
            a.url = await getLocalFile(a, a.storage, { expires: a.expires })(context) || a.url;
            return a;
        }));
        context.result = _set(context.result, 'data', joinedFiles);
    }
    return context;
};

export const handleGet = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    if (!context.params.joiningFile && !context.params.core?.skipJoins) {
        context.params = _set(context.params, 'joiningFile', {join: true});
        context.result.url = await getLocalFile(context.result, context.result.storage, { expires: context.result.expires})(context);
    }
    return context;
};

export const handleDelete = async (context: HookContext): Promise<HookContext> => {
    const uploadsConfig = context.app.get('uploads') as AnyObj;

    try {
        /**
         * Find document data
         */
        const currentDoc = await loadExists(context);
        context = setExists(context, currentDoc);
        context.params.fileId = currentDoc.fileId;
        context.params.uploadService = currentDoc.storage;
        context = await uploadFile(context);
    } catch (error) {
        // context.error(`try to remove ${context.id} from storage failed, method: ` + context.method);
        if (uploadsConfig.blockDeleteDocumentWhenDeleteFileFailed) {
            throw new GeneralError('Delete file failed');
        }
        context.error = new Error(`try to remove ${context.id} from storage failed, method: ${context.method}, Error: ${error}`);
    }
    return context;
};


// const addAppImage = async context => {
//   let { createdBy, updatedBy, _id, file, name } = context.result;
//   context.result.app_image = await context.app.service('app-files').create({
//     createdBy: createdBy,
//     updatedBy: updatedBy,
//     name: name ? name : file,
//     image: {
//       large: {
//         _id: _id,
//         file: file
//       }
//     },
//     tags: ['User Content']
//   })
//     .catch(err => {
//       console.log('unable to add app image', err.message);
//       return undefined;
//     });
//   return context;
// };


// const beforeHook = context => {
//   console.log('beforeHook - context.path:', context.path);
//   console.log('beforeHook - context.method:', context.method);
//   console.log('beforeHook - context.data:', context.data);
//   console.log('beforeHook - context.params:', context.params);
// };

// const afterHook = context => {
//   console.log('afterHook - context:', context);
// };

// const errorHook = context => {
//   console.log('errorHook - Error: ', context.error);
// };
