import {computed, ComputedRef, Ref} from 'vue';
import {AnyObj} from 'src/utils/types';
import {Org} from 'components/orgs/utils/control-groups.js';
import {Plan} from 'components/plans/utils/index.js';
import {$limitStr} from 'src/utils/global-methods.js';
import {useRoute} from 'vue-router';

type OrgObj = Org & AnyObj
type Options = {
    plans: Ref<Array<Plan>> | ComputedRef<Array<Plan>>
}
export const accountNav = (org: Ref<OrgObj> | ComputedRef<OrgObj>, {plans}: Options) => {
    const route = useRoute()

    const links = computed(() => {
        const rMeta = route.meta || {}
        const planObj: any = {};
            for (const plan of plans.value || []) {
                planObj[$limitStr(plan.name || '', 30, '...')] = {
                    on: rMeta.category === 'plans' && route.params.planId == plan._id,
                    link: {
                        route: {
                            name: 'plan-view',
                            params: {planId: plan._id}
                        }
                    },
                    subs: [
                        {
                            label: 'Elections',
                            on: rMeta.name === 'elections',
                            link: {
                                route: {
                                    name: 'plan-view',
                                    params: {planId: plan._id}
                                }
                            }
                        },
                        {
                            label: 'Coverage',
                            on: rMeta.name === 'coverage',
                            link: {
                                route: {
                                    name: 'plan-coverage',
                                    params: {planId: plan._id}
                                }
                            }
                        }
                    ]
                }
            }
            return [
                {
                    icon: 'mdi-heart',
                    label: 'Care',
                    on: rMeta.category === 'care',
                    link: {route: {name: 'my-care'}},
                    items: {
                        'Care': {
                            'Events': {
                                label: 'Events',
                                link: {
                                    route: {name: 'my-care'}
                                },
                                on: rMeta.category === 'care' && rMeta.name === 'events'
                            },
                            'Bills': {
                                label: 'Bills',
                                link: { route: { name: 'my-bills' }},
                                on: rMeta.category === 'care' && rMeta.name === 'claims',
                                subs: [
                                    {
                                        label: 'Claims',
                                        on: rMeta.category === 'care' && rMeta.sub === 'claims',
                                        link: {
                                            route: { name: 'my-bills'}
                                        }
                                    },
                                    {
                                        label: 'Uploads',
                                        on: rMeta.category === 'care' && rMeta.sub === 'uploads',
                                        link: { route: { name: 'claim-uploads'} }
                                    },
                                    {
                                        label: 'Payments',
                                        on: rMeta.category === 'care' && rMeta.sub === 'payments',
                                        link: { route: { name: 'claim-payments'} }
                                    }
                                ]
                            },
                            'Providers': {
                                label: 'Providers'
                            }
                        }
                    }
                },
                {
                    icon: 'mdi-hospital-box',
                    label: 'Health Plans',
                    on: rMeta.category === 'plans',
                    link: {route: {name: 'my-plans'}},
                    items: {
                        'Plans': planObj
                    }
                },
                {
                    icon: 'mdi-home',
                    label: 'Household',
                    on: rMeta.category === 'account-finance',
                    link: {route: {name: 'my-pay'}},
                    items: {
                        'Comp': {
                            'Pay': {
                                on: rMeta.name === 'my-pay',
                                link: {route: {name: 'org-comp'}}
                            }
                        }
                    }
                },
                {
                    icon: 'mdi-cog',
                    label: 'Settings',
                    on: rMeta.category === 'settings',
                    link: {
                        route: { name: 'org-settings' }
                    },
                    items: {
                        [org.value?.name || 'Company']: {
                            'Ownership': {
                                on: rMeta.name === 'org-ownership',
                                link: {route: {name: 'org-ownership'}}
                            },
                            'Control': {
                                on: rMeta.name === 'org-control',
                                link: {route: {name: 'org-control'}}
                            },
                            'Info': {
                                on: rMeta.name === 'org-info',
                                link: {route: {name: 'org-info'}}
                            }
                        }
                    }
                }
            ]
        }
    )

    return {
        links
    }

}

