
export const accountChildren = () => [
    {
        path: '',
        name: 'account-home',
        meta: {
            name: 'account-home'
        },
        component: () => import('src/components/orgs/pages/OrgPage.vue')
    },
    {
       path: 'finance',
       meta: {
           category: 'account-finance'
       },
       component: () => import('src/components/account/finance/pages/AccountFinance.vue'),
        children: [
            {
                path: ':tab?',
                name: 'my-pay',
                meta: {
                    name: 'my-pay'
                },
                component: () => import('src/components/account/finance/comp/MyComp.vue')
            }
        ]
    },
    {
        path: 'add-bill',
        name: 'quick-claim',
        meta: {
            category: 'care'
        },
        component: () => import('src/components/care/pages/QuickClaim.vue')
    },
    {
        path: '/care',
        meta: {
            category: 'care',
            ucan: true
        },
        component: () => import('src/components/care/pages/CarePage.vue'),
        children: [
            {
                path: '',
                name: 'my-care',
                meta: {
                    name: 'events'
                },
                component: () => import('src/components/care/pages/MyCare.vue')
            },
            {
                path: 'claims',
                meta: {
                    name: 'claims'
                },
                component: () => import('src/components/claims/pages/MyClaims.vue'),
                children: [
                    {
                        path: '',
                        name: 'my-bills',
                        meta: {sub: 'claims'},
                        component: () => import('src/components/claims/pages/ClaimsPage.vue')
                    },
                    {
                        path: 'uploads',
                        name: 'claim-uploads',
                        meta: {sub: 'uploads'},
                        component: () => import('src/components/claims/claim-requests/cards/ClaimsRequestDisplay.vue'),
                        props: {
                            showPatient: true
                        }
                    },
                    {
                        path: 'payments',
                        name: 'claim-payments',
                        meta: {sub: 'payments'},
                        component: () => import('src/components/claims/cards/ClaimPayments.vue')
                    }
                ].map(a => {
                    return {
                        ...a,
                        meta: {
                            ...a.meta,
                            category: 'care',
                            name: 'claims'
                        }
                    }
                })
            },
            {
                path: '/event/:careId',
                name: 'care-page',
                component: () => import('src/components/care/pages/CareDetail.vue'),
                meta: {
                    name: 'events'
                }
            },
            {
                path: '/visit/:visitId/:claimId?',
                name: 'visit-page',
                component: () => import('src/components/care/visits/pages/VisitPage.vue'),
                meta: {
                    name: 'claims'
                },
                props: {
                    context: 'participant'
                }
            }
        ].map(a => {
            return {
                ...a,
                meta: {
                    category: 'care',
                    ...a.meta
                }
            }
        })
    },
    {
        path: '/claim/:claimId',
        name: 'claim-page',
        component: () => import('src/components/claims/pages/ClaimPage.vue'),
    },
    {
        path: '/pay-claim/:claimId',
        name: 'pay-claim',
        component: () => import('src/components/claims/pages/PayClaimPage.vue')
    },
    {
        path: '/plans',
        name: 'my-plans',
        meta: {
            name: 'plans',
            category: 'plans'
        },
        component: () => import('src/components/orgs/pages/OrgPlans.vue')
    },
    {
        path: '/plans/:planId',
        meta: {
            category: 'plans'
        },
        component: () => import('src/components/plans/pages/PlanPage.vue'),
        children: [
            {
                path: '',
                name: 'plan-view',
                meta: {name: 'elections'},
                component: () => import('components/enrollments/admin/cards/MyEnrollments.vue')
            },
            {
                path: 'coverage',
                name: 'plan-coverage',
                meta: {name: 'coverage'},
                component: () => import('src/components/enrollments/cards/MyCoverages.vue')
            }
        ].map(a => {
            return {
                ...a,
                meta: {
                    ...a.meta,
                    category: 'plans'
                }
            }
        })
    },
    {
        path: '/org/:orgId/:tab?',
        name: 'org-admin',
        component: () => import('src/components/orgs/pages/OrgAdmin.vue')
    },
    {
        path: '/enroll/:enrollId/:tab?/:subTab?',
        name: 'enroll',
        component: () => import('src/components/enrollments/pages/EnrollHere.vue')
    },
    {
        path: '/plan-docs/:planId?',
        name: 'plan-docs',
        component: () => import('src/components/plans/docs/pages/PlanDocs.vue')
    },
    {
        path: '/plan-print/:tab?',
        name: 'doc-print',
        component: () => import('src/components/plans/docs/pages/DocPrint.vue')
    },
    {
        path: '/doc-templates/:docId?',
        name: 'doc-templates',
        component: () => import('src/components/plans/docs/pages/DocTemplates.vue')
    },
    {
        path: 'ichra/:enrollmentId/:tab?',
        name: 'ichra-enroll',
        component: () => import('src/components/enrollments/ichra/pages/IchraEnroll.vue')
    }
]
export const accountRoutes = () => {
    return [
        {
            path: '/',
            component: () => import('src/layouts/AccountLayout.vue'),
            meta: {
                ucan: true
            },
            children: accountChildren()
        }
    ]
}
